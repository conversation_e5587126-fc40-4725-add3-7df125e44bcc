# 收藏夹功能迁移指南

## 📋 概述

为了简化代码结构并减少不必要的中间层，我们移除了 `main_controller` 和 `controller` 中的收藏夹代理方法。现在需要直接使用专门的 `collection_controller` 实例。

## 🔄 迁移步骤

### 旧的调用方式（已废弃）

```python
# ❌ 不再支持 - 通过 main_controller 的代理方法
from controllers.douyin import douyin_controller

# 这些方法已被移除
await douyin_controller.get_self_aweme_collection_rpc_with_cookies(cursor=0, count=20, cookies="...")
await douyin_controller.get_collect_video_list_rpc_with_cookies("collection_id", 0, 20, "...")
await douyin_controller.sync_single_collection_with_cookies("collection_id", "...")
await douyin_controller.sync_and_save_single_collection_with_cookies("collection_id", "...")
```

### 新的调用方式（推荐）

#### 方式一：直接使用 collection_controller 实例

```python
# ✅ 推荐 - 直接使用 collection_controller
from controllers.douyin.collection import collection_controller
from rpc.douyin.schemas import SelfAwemeCollectionRequest, CollectVideoListRequest

# 获取收藏夹列表
request = SelfAwemeCollectionRequest(cursor=0, count=20)
response = await collection_controller.get_self_aweme_collection_rpc_with_cookies(request, cookies="...")

# 获取收藏视频列表
request = CollectVideoListRequest(collects_id="collection_id", cursor=0, count=20)
videos = await collection_controller.get_collect_video_list_rpc_with_cookies(request, cookies="...")

# 同步收藏夹（不保存到数据库）
result = await collection_controller.sync_single_collection_with_cookies("collection_id", cookies="...")

# 同步收藏夹并保存到数据库
result = await collection_controller.sync_and_save_single_collection_with_cookies("collection_id", cookies="...")
```

#### 方式二：通过 main_controller 获取 collection_controller

```python
# ✅ 也可以 - 通过 main_controller 获取专门的控制器
from controllers.douyin import douyin_controller

# 获取专门的 collection 控制器
collection_ctrl = douyin_controller.get_collection_controller()

# 然后使用相同的方法调用
request = SelfAwemeCollectionRequest(cursor=0, count=20)
response = await collection_ctrl.get_self_aweme_collection_rpc_with_cookies(request, cookies="...")
```

## 🔧 主要变化

### 1. 方法签名变化

**旧的简化签名（已移除）：**
```python
# 直接传递参数
await get_self_aweme_collection_rpc_with_cookies(cursor: int, count: int, cookies: str)
await get_collect_video_list_rpc_with_cookies(collects_id: str, cursor: int, count: int, cookies: str)
```

**新的标准签名：**
```python
# 使用请求对象
await get_self_aweme_collection_rpc_with_cookies(request: SelfAwemeCollectionRequest, cookies: str)
await get_collect_video_list_rpc_with_cookies(request: CollectVideoListRequest, cookies: str)
```

### 2. 导入变化

**旧的导入：**
```python
from controllers.douyin import douyin_controller
# 然后通过 douyin_controller 调用收藏夹方法
```

**新的导入：**
```python
# 方式一：直接导入
from controllers.douyin.collection import collection_controller

# 方式二：通过 main_controller 获取
from controllers.douyin import douyin_controller
collection_ctrl = douyin_controller.get_collection_controller()
```

## 📝 迁移检查清单

- [ ] 更新所有收藏夹相关的方法调用
- [ ] 更新导入语句
- [ ] 将简化参数改为请求对象
- [ ] 测试所有收藏夹功能
- [ ] 更新相关文档和注释

## 💡 最佳实践

1. **优先使用方式一**：直接导入 `collection_controller` 更简洁明确
2. **统一请求对象**：使用标准的请求对象而不是分散的参数
3. **错误处理**：collection_controller 提供了更好的错误处理和日志记录
4. **性能考虑**：减少了中间层调用，性能更好

## 🚨 注意事项

- 所有示例文件和文档已经更新为新的调用方式
- API 路由文件已经直接使用 `DouyinCollectionController` 实例
- 如果你的代码中还在使用旧的代理方法，需要立即更新
- 新的方法签名更加标准化，与 RPC 接口保持一致

## 🔗 相关文档

- [collection_controller 详细文档](./collection_controller_documentation.md)
- [使用示例](../examples/)
- [API 文档](../../../../api/v1/douyin/collection_router.py)
